<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Üyeler yükleniyor...">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
    <!-- Main Content -->
    <div class="col-md-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Büt<PERSON><PERSON> Üyeler</h5>
          <div class="d-flex align-items-center gap-3">
            <!-- Toplam Kayıtlı Üye Sayısı -->
            <div class="total-members-badge">
              <span class="modern-badge modern-badge-primary">
                <i class="fas fa-users me-2"></i>
                Toplam: {{ totalRegisteredMembers }} Kullanıcı
              </span>
            </div>

          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filter -->
          <div class="row mb-4">
            <div class="col-md-8">
              <div class="search-input-container">
                <i class="fas fa-search search-icon"></i>
                <input
                  type="text"
                  class="search-input"
                  [(ngModel)]="searchText"
                  (input)="onSearch($event)"
                  placeholder="Ad, Soyad veya Telefon ile arama yapın..."
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="d-flex justify-content-end">
                <div class="btn-group">
                  <button class="btn-modern btn-modern-outline" [class.active]="viewMode === 'table'" (click)="setViewMode('table')">
                    <i class="fas fa-table"></i> Tablo
                  </button>
                  <button class="btn-modern btn-modern-outline" [class.active]="viewMode === 'card'" (click)="setViewMode('card')">
                    <i class="fas fa-th-large"></i> Kart
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Gender Filter -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="d-flex justify-content-start align-items-center">
                <span class="filter-label me-3">Cinsiyet:</span>
                <div class="btn-group">
                  <button
                    class="btn-modern btn-modern-outline btn-modern-sm"
                    [class.active]="selectedGender === null"
                    [disabled]="genderFilterDisabled"
                    (click)="onGenderFilterChange(null)">
                    Tümü
                  </button>
                  <button
                    class="btn-modern btn-modern-outline btn-modern-sm"
                    [class.active]="selectedGender === 1"
                    [disabled]="genderFilterDisabled"
                    (click)="onGenderFilterChange(1)">
                    Erkek
                  </button>
                  <button
                    class="btn-modern btn-modern-outline btn-modern-sm"
                    [class.active]="selectedGender === 2"
                    [disabled]="genderFilterDisabled"
                    (click)="onGenderFilterChange(2)">
                    Kadın
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Active Filters -->
          <div class="filter-tags mb-4" *ngIf="searchText || selectedGender !== null">
            <div class="filter-tag" *ngIf="searchText">
              <span>Arama: {{ searchText }}</span>
              <span class="remove-tag" (click)="clearSearch()">×</span>
            </div>
             <div class="filter-tag" *ngIf="selectedGender !== null">
              <span>Cinsiyet: {{ selectedGender === 1 ? 'Erkek' : 'Kadın' }}</span>
              <span class="remove-tag"
                    [class.disabled]="genderFilterDisabled"
                    (click)="!genderFilterDisabled && onGenderFilterChange(null)">×</span>
            </div>
          </div>

          <!-- Table View -->
          <div class="table-responsive" *ngIf="viewMode === 'table'">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>Ad Soyad</th>
                  <th>Telefon</th>
                  <th class="text-center">İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="staggered-item">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar-circle" [style.background-color]="getGenderColor(member.gender)">
                        <img
                          *ngIf="getProfileImageUrl(member)"
                          [src]="getProfileImageUrl(member)"
                          [alt]="member.name"
                          class="profile-image"
                          (error)="onProfileImageError($event)"
                          (load)="onProfileImageLoad($event)"
                        />
                        <i
                          *ngIf="!getProfileImageUrl(member)"
                          class="fas fa-user"
                        ></i>
                      </div>
                      <div class="ms-3">{{ member.name }}</div>
                    </div>
                  </td>
                  <td>{{ member.phoneNumber }}</td>
                  <td>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-info btn-modern-icon btn-modern-icon-sm"
                        title="Detaylar"
                        (click)="viewMemberDetails(member)"
                      >
                        <i class="fas fa-info"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-primary btn-modern-icon btn-modern-icon-sm"
                        title="Güncelle"
                        (click)="openUpdateDialog(member)"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-success btn-modern-icon btn-modern-icon-sm"
                        title="Mesaj Gönder"
                        (click)="openWhatsApp(member.phoneNumber)"
                      >
                        <i class="fas fa-comment"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-danger btn-modern-icon btn-modern-icon-sm"
                        title="Sil"
                        (click)="onDelete(member)"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Card View -->
          <div *ngIf="viewMode === 'card'">
            <div class="row">
              <div class="col-md-4 col-lg-3 mb-4" *ngFor="let member of members">
                <div class="modern-card h-100 staggered-item">
                  <div class="card-body text-center">
                    <div class="avatar-circle-lg mx-auto mb-3" [style.background-color]="getGenderColor(member.gender)">
                      <img
                        *ngIf="getProfileImageUrl(member)"
                        [src]="getProfileImageUrl(member)"
                        [alt]="member.name"
                        class="profile-image-lg"
                        (error)="onProfileImageError($event)"
                        (load)="onProfileImageLoad($event)"
                      />
                      <i
                        *ngIf="!getProfileImageUrl(member)"
                        class="fas fa-user"
                      ></i>
                    </div>
                    <h5 class="mb-1">{{ member.name }}</h5>
                    <p class="text-muted mb-3">{{ member.phoneNumber }}</p>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-info btn-modern-icon"
                        title="Detaylar"
                        (click)="viewMemberDetails(member)"
                      >
                        <i class="fas fa-info"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-primary btn-modern-icon"
                        title="Güncelle"
                        (click)="openUpdateDialog(member)"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-success btn-modern-icon"
                        title="Mesaj Gönder"
                        (click)="openWhatsApp(member.phoneNumber)"
                      >
                        <i class="fas fa-comment"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-danger btn-modern-icon"
                        title="Sil"
                        (click)="onDelete(member)"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Results -->
          <div class="text-center py-5" *ngIf="members.length === 0">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5>Sonuç Bulunamadı</h5>
            <p class="text-muted">Arama kriterlerinize uygun üye bulunamadı.</p>
            <button class="btn-modern btn-modern-primary" (click)="clearSearch()">
              Filtreleri Temizle
            </button>
          </div>

          <!-- Pagination -->
          <div class="modern-pagination" *ngIf="totalPages > 1">
            <ul class="pagination">
              <li class="page-item" [class.disabled]="currentPage === 1">
                <a
                  class="page-link"
                  (click)="onPageChange(currentPage - 1)"
                  href="javascript:void(0)"
                >
                  <i class="fas fa-chevron-left"></i>
                </a>
              </li>
              <li
                class="page-item"
                *ngFor="let page of getPaginationRange(); let i = index"
                [class.active]="currentPage === page"
              >
                <a
                  class="page-link"
                  (click)="onPageChange(page)"
                  href="javascript:void(0)"
                >
                  {{ page }}
                </a>
              </li>
              <li
                class="page-item"
                [class.disabled]="currentPage === totalPages"
              >
                <a
                  class="page-link"
                  (click)="onPageChange(currentPage + 1)"
                  href="javascript:void(0)"
                >
                  <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    </div>

  </div>
</div>
