<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Borçlu üyeler yükleniyor...">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

  <div [class.content-blur]="isLoading">
    <!-- Dashboard Summary -->
    <div class="row mb-4">
      <div class="col-md-4">
        <div class="modern-stats-card bg-danger-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getTotalDebtorCount() }}</div>
            <div class="modern-stats-label">Toplam Borçlu Üye</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-warning-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getTotalOriginalDebt() | currency:'₺':'symbol':'1.2-2':'tr' }}</div>
            <div class="modern-stats-label">Toplam Borç</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-info-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-hand-holding-usd"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getTotalRemainingDebt() | currency:'₺':'symbol':'1.2-2':'tr' }}</div>
            <div class="modern-stats-label">Kalan Borç</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="modern-card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fas fa-user-tag me-3 ms-3"></i>
          Borçlu Üyeler
        </h5>
        <div class="header-actions">
          <div class="input-group search-box">
            <div class="input-group-prepend">
              <span class="input-group-text"><i class="fas fa-search"></i></span>
            </div>
            <input 
              type="text" 
              class="form-control" 
              [(ngModel)]="searchText"
              (ngModelChange)="filterDebtorMembers()"
              placeholder="Üye ara..."
            >
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="modern-table">
            <thead>
              <tr>
                <th>Üye Adı</th>
                <th>Telefon</th>
                <th>Toplam Borç</th>
                <th>Kalan Borç</th>
                <th>Ödeme Oranı</th>
                <th (click)="toggleSort('lastUpdateDate')" style="cursor: pointer;">
                  Son Güncelleme
                  <i class="fas ms-1"
                     [ngClass]="{
                       'fa-sort': sortColumn !== 'lastUpdateDate',
                       'fa-sort-up': sortColumn === 'lastUpdateDate' && sortDirection === 'asc',
                       'fa-sort-down': sortColumn === 'lastUpdateDate' && sortDirection === 'desc'
                     }"></i>
                </th>
                <th class="text-center">İşlem</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let debt of filteredDebtorMembers">
                <td>
                  <div class="member-info">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(debt.memberName)">
                      {{ getInitials(debt.memberName) }}
                    </div>
                    <div>{{ debt.memberName }}</div>
                  </div>
                </td>
                <td>{{ debt.phoneNumber }}</td>
                <td>{{ debt.originalAmount | currency:'₺':'symbol':'1.2-2':'tr' }}</td>
                <td>
                  <span [ngClass]="{'text-danger': debt.remainingAmount > 0}">
                    {{ debt.remainingAmount | currency:'₺':'symbol':'1.2-2':'tr' }}
                  </span>
                </td>
                <td>
                  <div class="progress-container">
                    <div class="progress">
                      <div 
                        class="progress-bar" 
                        [ngClass]="getProgressBarClass(debt)"
                        [style.width.%]="getPaymentPercentage(debt)"
                      ></div>
                    </div>
                    <span class="progress-text">{{ getPaymentPercentage(debt) }}%</span>
                  </div>
                </td>
                <td>{{ debt.lastUpdateDate | date:'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">
                  <button 
                    class="modern-btn modern-btn-primary"
                    (click)="openPaymentDialog(debt)">
                    <i class="fas fa-hand-holding-usd me-1"></i> Ödeme Al
                  </button>
                </td>
              </tr>
              <tr *ngIf="filteredDebtorMembers.length === 0">
                <td colspan="7" class="text-center py-4">
                  <div class="empty-state">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <h5>Borçlu üye bulunamadı</h5>
                    <p class="text-muted">Şu anda borçlu üye bulunmamaktadır.</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

  </div>
</div>
