/* Membership Add Component Styles */

/* Last Membership Info */
.last-membership-info {
  margin-top: var(--spacing-xs);
  font-size: 0.85rem;
  color: var(--danger);
  font-weight: 500;
}

/* Form Section */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.section-title {
  margin-bottom: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  display: flex;
  align-items: center;
  font-size: 1rem;
}

/* Modern Form Group */
.modern-form-group {
  margin-bottom: var(--spacing-md);
  position: relative;
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.modern-form-label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Input Group Styles */
.input-group {
  display: flex;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
  border-right: none;
}

.modern-form-control {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Error States */
.modern-form-control.ng-invalid.ng-touched {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.input-group-text.text-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Shake Animation */
.shake-animation {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  border-color: var(--danger) !important;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }
  20%, 40%, 60%, 80% { transform: translateX(6px); }
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Progress Bar */
.progress {
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

.progress-bar {
  background-color: var(--primary);
  transition: width 0.3s ease;
}

/* Dark Mode Support */
[data-theme="dark"] .content-blur {
  filter: blur(3px) brightness(0.7);
}

[data-theme="dark"] .form-section {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .form-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .input-group-text {
  background-color: var(--primary-light);
  border-color: var(--border-color);
  color: var(--primary);
}

[data-theme="dark"] .progress {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-btn-primary:disabled {
  background-color: #4a5568;
}