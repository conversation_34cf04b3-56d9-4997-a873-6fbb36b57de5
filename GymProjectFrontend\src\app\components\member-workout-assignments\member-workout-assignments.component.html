<div class="container-fluid" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Program atamaları yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <!-- Page Header -->
    <div class="page-header">
  <div class="page-title-container">
    <h1 class="page-title">
      <fa-icon [icon]="faUsers" class="page-icon"></fa-icon>
      Üye Program Atamaları
    </h1>
    <p class="page-subtitle">Üyelere antrenman programı atama ve yönetim sistemi</p>
  </div>
  
  <div class="page-actions">
    <button 
      class="modern-btn modern-btn-primary"
      (click)="openAssignModal()"
      *ngIf="isOwner || isAdmin">
      <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon>
      Program Ata
    </button>
  </div>
</div>


<!-- Assignments Table -->
<div class="modern-card">
  <div class="modern-card-header">
    <h5>Program Atamaları</h5>

    <!-- Search Container -->
    <div class="search-container">
      <div class="modern-form-group">
        <div class="search-input-wrapper">
          <fa-icon [icon]="faSearch" class="search-icon"></fa-icon>
          <input
            type="text"
            class="modern-form-control search-input"
            placeholder="Üye adı veya program adı ile arama yapın..."
            (input)="onSearch($event)"
            [value]="searchText">
        </div>
      </div>
    </div>
  </div>
  <div class="modern-card-body">
    <!-- Empty State -->
    <div *ngIf="filteredAssignments.length === 0" class="empty-state">
      <div class="empty-icon">
        <fa-icon [icon]="faUsers"></fa-icon>
      </div>
      <h3>Program ataması bulunamadı</h3>
      <p>Henüz hiç program ataması yapılmamış veya arama kriterlerinize uygun sonuç bulunamadı.</p>
      <button 
        class="modern-btn modern-btn-primary"
        (click)="openAssignModal()"
        *ngIf="isOwner || isAdmin">
        <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon>
        İlk Program Atamasını Yap
      </button>
    </div>

    <!-- Table -->
    <div *ngIf="filteredAssignments.length > 0" class="table-responsive">
      <table class="modern-table">
        <thead>
          <tr>
            <th>Üye Adı</th>
            <th>Program Adı</th>
            <th>Seviye</th>
            <th>Hedef</th>
            <th>Başlangıç Tarihi</th>
            <th>İşlemler</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let assignment of getPaginatedAssignments()">
            <td>
              <div class="member-info">
                <strong>{{ assignment.memberName }}</strong>
              </div>
            </td>
            <td>
              <div class="program-info">
                <strong>{{ assignment.programName }}</strong>
              </div>
            </td>
            <td>
              <span class="modern-badge modern-badge-info" *ngIf="assignment.experienceLevel">
                {{ assignment.experienceLevel }}
              </span>
              <span *ngIf="!assignment.experienceLevel">-</span>
            </td>
            <td>
              <span class="modern-badge modern-badge-warning" *ngIf="assignment.targetGoal">
                {{ assignment.targetGoal }}
              </span>
              <span *ngIf="!assignment.targetGoal">-</span>
            </td>
            <td>{{ formatDate(assignment.startDate) }}</td>
            <td>
              <div class="action-buttons">
                <button
                  class="modern-btn modern-btn-sm modern-btn-outline-primary"
                  (click)="editAssignment(assignment)"
                  *ngIf="isOwner || isAdmin"
                  title="Düzenle">
                  <fa-icon [icon]="faEdit"></fa-icon>
                </button>
                <button
                  class="modern-btn modern-btn-sm modern-btn-outline-danger"
                  (click)="deleteAssignment(assignment)"
                  *ngIf="isOwner || isAdmin"
                  title="Sil">
                  <fa-icon [icon]="faTrashAlt"></fa-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="filteredAssignments.length > 0 && totalPages > 1" class="pagination-container">
      <nav>
        <ul class="modern-pagination">
          <li class="modern-page-item" [class.disabled]="currentPage === 1">
            <a class="modern-page-link" (click)="onPageChange(currentPage - 1)" *ngIf="currentPage > 1">
              <i class="fas fa-chevron-left"></i>
            </a>
          </li>
          
          <li class="modern-page-item" 
              *ngFor="let page of [].constructor(totalPages); let i = index"
              [class.active]="currentPage === i + 1">
            <a class="modern-page-link" (click)="onPageChange(i + 1)">
              {{ i + 1 }}
            </a>
          </li>
          
          <li class="modern-page-item" [class.disabled]="currentPage === totalPages">
            <a class="modern-page-link" (click)="onPageChange(currentPage + 1)" *ngIf="currentPage < totalPages">
              <i class="fas fa-chevron-right"></i>
            </a>
          </li>
        </ul>
      </nav>
      
      <div class="pagination-info">
        Sayfa {{ currentPage }} / {{ totalPages }} ({{ totalItems }} kayıt)
      </div>
    </div>

  </div>
</div>
