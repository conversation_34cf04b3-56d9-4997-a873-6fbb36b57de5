<div class="exercise-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="page-title-container">
      <h1 class="page-title">
        <fa-icon [icon]="faDumbbell" class="page-icon"></fa-icon>
        Egzersiz Listesi
      </h1>
      <p class="page-subtitle">Egzersizleri görüntüleme ve yönetim paneli</p>
    </div>
    <div class="page-actions">
      <button
        class="modern-btn modern-btn-primary"
        (click)="openAddExerciseModal()"
        *ngIf="isAdmin || isOwner">
        <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon>
        Yeni Egzersiz
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="row">
      <!-- View Mode -->
      <div class="col-md-3">
        <label class="form-label">G<PERSON><PERSON><PERSON>nüm</label>
        <select class="form-select" [(ngModel)]="viewMode" (change)="onViewModeChange()">
          <option value="combined">Tüm Egzersizler</option>
          <option value="system">Sistem Egzersizleri</option>
          <option value="company">Salon Egzersizleri</option>
        </select>
      </div>

      <!-- Category Filter -->
      <div class="col-md-3">
        <label class="form-label">Kategori</label>
        <select class="form-select" [(ngModel)]="selectedCategoryId" (change)="onCategoryChange()">
          <option value="">Tüm Kategoriler</option>
          <option *ngFor="let category of categories" [value]="category.exerciseCategoryID">
            {{category.categoryName}}
          </option>
        </select>
      </div>

      <!-- Difficulty Filter -->
      <div class="col-md-3">
        <label class="form-label">Zorluk Seviyesi</label>
        <select class="form-select" [(ngModel)]="selectedDifficultyLevel" (change)="onDifficultyChange()">
          <option value="">Tüm Seviyeler</option>
          <option [value]="1">Başlangıç</option>
          <option [value]="2">Orta</option>
          <option [value]="3">İleri</option>
        </select>
      </div>

      <!-- Equipment Filter -->
      <div class="col-md-3">
        <label class="form-label">Ekipman</label>
        <input
          type="text"
          class="form-control"
          placeholder="Ekipman ara..."
          [value]="selectedEquipment"
          (input)="onEquipmentChange($event)">
      </div>
    </div>

    <div class="row mt-3">
      <!-- Search -->
      <div class="col-md-6">
        <label class="form-label">Arama</label>
        <div class="input-group">
          <span class="input-group-text">
            <fa-icon [icon]="faSearch"></fa-icon>
          </span>
          <input 
            type="text" 
            class="form-control" 
            placeholder="Egzersiz adı, açıklama veya kas grubu ara..."
            [value]="searchText"
            (input)="onSearch($event)">
        </div>
      </div>

      <!-- Clear Filters -->
      <div class="col-md-6 d-flex align-items-end">
        <button
          class="btn btn-outline-secondary"
          (click)="clearFilters()"
          [disabled]="!hasActiveFilters()">
          <fa-icon [icon]="faFilter"></fa-icon>
          Filtreleri Temizle
        </button>
      </div>
    </div>
  </div>

  <!-- Results Info -->
  <div class="results-info">
    <span class="text-muted">
      Toplam {{totalItems}} egzersiz bulundu
      <span *ngIf="searchText || selectedCategoryId || selectedDifficultyLevel || selectedEquipment">
        (filtrelenmiş)
      </span>
    </span>
  </div>

  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Egzersizler yükleniyor...">
  </app-loading-spinner>

  <!-- Exercise Cards -->
  <div *ngIf="!isLoading" class="exercises-container">
    <div class="row g-4">
      <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12" *ngFor="let exercise of exercises">
        <div class="exercise-card h-100">
          <!-- Exercise Type Badge -->
          <div class="exercise-type-badge">
            <span class="badge" [ngClass]="getExerciseTypeBadgeClass(exercise.exerciseType)">
              {{exercise.exerciseType === 'System' ? 'Sistem' : 'Salon'}}
            </span>
          </div>

          <!-- Exercise Header -->
          <div class="exercise-header">
            <div class="exercise-header-content">
              <h5 class="exercise-name">{{exercise.exerciseName}}</h5>
              <div class="exercise-meta">
                <span class="category-badge">{{exercise.categoryName}}</span>
                <span
                  class="difficulty-badge badge"
                  [ngClass]="getDifficultyBadgeClass(exercise.difficultyLevel)"
                  *ngIf="exercise.difficultyLevelText">
                  {{exercise.difficultyLevelText}}
                </span>
              </div>
            </div>
          </div>

          <!-- Exercise Content -->
          <div class="exercise-content">
            <p class="exercise-description" *ngIf="exercise.description">
              {{exercise.description | slice:0:120}}{{exercise.description && exercise.description.length > 120 ? '...' : ''}}
            </p>

            <div class="exercise-details" *ngIf="exercise.muscleGroups || exercise.equipment">
              <div class="detail-item" *ngIf="exercise.muscleGroups">
                <strong>Kas Grupları:</strong> {{exercise.muscleGroups}}
              </div>
              <div class="detail-item" *ngIf="exercise.equipment">
                <strong>Ekipman:</strong> {{exercise.equipment}}
              </div>
            </div>

            <div class="exercise-instructions" *ngIf="exercise.instructions">
              <strong>Talimatlar:</strong>
              <p class="instructions-text">{{exercise.instructions | slice:0:100}}{{exercise.instructions && exercise.instructions.length > 100 ? '...' : ''}}</p>
            </div>
          </div>

          <!-- Exercise Actions -->
          <div class="exercise-actions" *ngIf="(isAdmin || isOwner) && exercise.exerciseType === 'Company'">
            <button
              class="btn btn-sm btn-outline-primary"
              title="Düzenle"
              (click)="openEditExerciseModal(exercise)">
              <fa-icon [icon]="faEdit"></fa-icon>
            </button>
            <button
              class="btn btn-sm btn-outline-danger"
              title="Sil"
              (click)="deleteExercise(exercise)">
              <fa-icon [icon]="faTrashAlt"></fa-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && exercises.length === 0" class="empty-state">
    <div class="empty-icon">
      <fa-icon [icon]="faInfoCircle" size="3x"></fa-icon>
    </div>
    <h4>Egzersiz Bulunamadı</h4>
    <p class="text-muted">
      <span *ngIf="searchText || selectedCategoryId || selectedDifficultyLevel || selectedEquipment">
        Arama kriterlerinize uygun egzersiz bulunamadı. Filtreleri değiştirmeyi deneyin.
      </span>
      <span *ngIf="!(searchText || selectedCategoryId || selectedDifficultyLevel || selectedEquipment)">
        Henüz hiç egzersiz eklenmemiş.
      </span>
    </p>
    <button 
      class="btn btn-primary" 
      (click)="openAddExerciseModal()"
      *ngIf="(isAdmin || isOwner) && !(searchText || selectedCategoryId || selectedDifficultyLevel || selectedEquipment)">
      <fa-icon [icon]="faPlus"></fa-icon>
      İlk Egzersizi Ekle
    </button>
  </div>

  <!-- Pagination -->
  <nav *ngIf="!isLoading && exercises.length > 0 && totalPages > 1" class="pagination-nav">
    <ul class="pagination justify-content-center">
      <li class="page-item" [class.disabled]="currentPage === 1">
        <button class="page-link" (click)="onPageChange(currentPage - 1)" [disabled]="currentPage === 1">
          Önceki
        </button>
      </li>
      
      <li class="page-item" *ngFor="let page of getPaginationRange()" [class.active]="page === currentPage">
        <button class="page-link" (click)="onPageChange(page)">{{page}}</button>
      </li>
      
      <li class="page-item" [class.disabled]="currentPage === totalPages">
        <button class="page-link" (click)="onPageChange(currentPage + 1)" [disabled]="currentPage === totalPages">
          Sonraki
        </button>
      </li>
    </ul>
  </nav>
</div>
