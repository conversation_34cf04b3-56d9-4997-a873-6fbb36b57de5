/* Member Workout Assignments Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--border-radius-lg);
  color: white;
}

.page-title-container {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-icon {
  font-size: 1.5rem;
}

.page-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Statistics Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* Search Container in Header */
.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.search-container {
  flex: 1;
  min-width: 350px;
  max-width: 600px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  color: var(--text-secondary);
  z-index: 1;
  font-size: 0.9rem;
}

.search-input {
  padding-left: 2.5rem !important;
  width: 100%;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

.member-info strong {
  color: var(--text-primary);
  font-weight: 600;
}

.program-info strong {
  color: var(--primary);
  font-weight: 600;
}

.program-stats {
  text-align: center;
}

.program-stats small {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
}

.action-buttons .modern-btn {
  min-width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--text-secondary);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  margin-bottom: var(--spacing-lg);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .page-actions {
    width: 100%;
    justify-content: stretch;
  }

  .page-actions .modern-btn {
    flex: 1;
  }

  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .search-container {
    min-width: 0;
    max-width: none;
    width: 100%;
    order: 2;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .modern-pagination {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .modern-table th,
  .modern-table td {
    padding: var(--spacing-sm);
  }

  .action-buttons .modern-btn {
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

[data-theme="dark"] .member-info strong {
  color: var(--text-primary);
}

[data-theme="dark"] .program-info strong {
  color: var(--primary);
}

[data-theme="dark"] .program-stats small {
  color: var(--text-secondary);
}

[data-theme="dark"] .loading-spinner {
  color: var(--text-secondary);
}

[data-theme="dark"] .empty-state {
  color: var(--text-secondary);
}

[data-theme="dark"] .empty-state h3 {
  color: var(--text-primary);
}

[data-theme="dark"] .pagination-info {
  color: var(--text-secondary);
}

[data-theme="dark"] .search-icon {
  color: var(--text-secondary);
}

[data-theme="dark"] .search-input {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}
