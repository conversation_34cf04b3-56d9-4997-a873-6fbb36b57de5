/* Membership Type Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
}

/* Sort Button */
.sort-btn {
  background: none;
  border: none;
  color: inherit;
  padding: 0 5px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-btn:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.sort-btn:focus {
  outline: none;
  box-shadow: none;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s var(--transition-timing);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.zoom-in {
  transition: transform 0.3s ease;
}

.zoom-in:hover {
  transform: scale(1.02);
}

/* Dark Mode Support */
[data-theme="dark"] .sort-btn {
  color: var(--text-primary);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .modern-card-header button {
    margin-top: 1rem;
    width: 100%;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
  
  .action-buttons button:first-child {
    margin-top: 0;
  }
}
