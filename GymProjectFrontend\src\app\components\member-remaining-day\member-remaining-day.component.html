<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Üyelik süresi yaklaşan üyeler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <!-- Main Card -->
    <div class="modern-card">
      <div class="modern-card-header">
        <div class="header-title">
          <h5 class="mb-0">
            <i class="fas fa-hourglass-half me-2 text-primary"></i>
            Üyelik Bitişi Yaklaşanlar (7 Gün ve Altı)
          </h5>
          <p class="text-muted mb-0 mt-1">
            Üyelik süresi 7 gün ve altında olan üyelerin listesi
            <span class="badge bg-primary ms-2">{{ getFilteredMembersCount() }} üye</span>
          </p>
        </div>
        <div class="header-actions">
          <div class="search-container">
            <div class="modern-search-box">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                class="modern-form-control"
                placeholder="Üye ara..."
                [(ngModel)]="searchText"
                (ngModelChange)="filterMembers()"
              >
              <button *ngIf="searchText" class="clear-search" (click)="clearSearch()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modern-card-body">
        <!-- Empty State -->
        <div *ngIf="filteredMembers.length === 0 && !isLoading" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-calendar-check fa-4x"></i>
          </div>
          <h4>{{ searchText ? 'Arama sonucu bulunamadı' : 'Yaklaşan üyelik bitişi bulunamadı' }}</h4>
          <p class="text-muted">
            {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : '7 gün ve altında üyeliği olan üye bulunmamaktadır.' }}
          </p>
          <button *ngIf="searchText" class="modern-btn modern-btn-outline-primary" (click)="clearSearch()">
            <i class="fas fa-times me-1"></i>
            Aramayı Temizle
          </button>
        </div>

        <!-- Members Table -->
        <div *ngIf="filteredMembers.length > 0" class="table-responsive">
          <div class="table-header mb-3">
            <div class="d-flex justify-content-between align-items-center">
              <span class="member-count">{{ filteredMembers.length }} üye bulundu</span>
              <button class="sort-toggle-btn" (click)="toggleSort()" title="Sıralama Yönünü Değiştir">
                <i class="fas" [ngClass]="sortDirection === 'asc' ? 'fa-sort-amount-down' : 'fa-sort-amount-up'"></i>
                <span class="ms-1">{{ sortDirection === 'asc' ? 'Artan' : 'Azalan' }}</span>
              </button>
            </div>
          </div>

          <table class="modern-table">
            <thead>
              <tr>
                <th>
                  <i class="fas fa-user me-2"></i>
                  Üye Adı
                </th>
                <th>
                  <i class="fas fa-phone me-2"></i>
                  Telefon
                </th>
                <th>
                  <i class="fas fa-dumbbell me-2"></i>
                  Branş
                </th>
                <th>
                  <i class="fas fa-calendar-times me-2"></i>
                  Kalan Gün
                </th>
                <th class="text-center">
                  <i class="fas fa-cogs me-2"></i>
                  İşlem
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let member of filteredMembers; trackBy: trackByMemberId"
                  [ngClass]="getTableRowClass(member.remainingDays)">
                <td>
                  <div class="member-info">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(member.memberName)">
                      {{ getInitials(member.memberName) }}
                    </div>
                    <div class="member-details">
                      <div class="member-name">{{ member.memberName }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="phone-number">{{ member.phoneNumber }}</span>
                </td>
                <td>
                  <span class="branch-name">{{ member.branch }}</span>
                </td>
                <td>
                  <span class="remaining-days-badge" [ngClass]="getRemainingDaysClass(member.remainingDays)">
                    {{ member.remainingDays }} Gün
                  </span>
                </td>
                <td class="text-center">
                  <button
                    class="modern-btn modern-btn-outline-success modern-btn-sm"
                    (click)="openWhatsApp(member.phoneNumber)"
                    title="WhatsApp ile mesaj gönder"
                  >
                    <i class="fab fa-whatsapp me-1"></i>
                    Mesaj Gönder
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

  </div>
</div>
