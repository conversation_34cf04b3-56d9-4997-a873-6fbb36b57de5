<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-8 offset-md-2">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">
              <i class="fas fa-user-circle"></i>
              Profil Bilgilerim
            </h2>
          </div>

          <div class="card-body">
            <div *ngIf="isLoading" class="text-center p-5">
              <app-loading-spinner [size]="'medium'" [showText]="true" [text]="'Profil bilgileri yükleniyor'"></app-loading-spinner>
            </div>

            <div *ngIf="!isLoading && userProfile" class="profile-container">
              <!-- Profil Fotoğrafı Bölümü -->
              <div class="profile-section">
                <h3 class="section-title">
                  <i class="fas fa-camera"></i>
                  Profil Fotoğrafı
                </h3>

                <div class="profile-image-container">
                  <div class="profile-image-wrapper">
                    <!-- Mevcut profil fotoğrafı veya varsayılan ikon -->
                    <div class="profile-image-display">
                      <img *ngIf="profileImageUrl"
                           [src]="profileImageUrl"
                           alt="Profil Fotoğrafı"
                           class="profile-image"
                           (error)="profileImageUrl = null">
                      <div *ngIf="!profileImageUrl" class="default-profile-icon">
                        <i class="fas fa-user"></i>
                      </div>
                    </div>

                    <!-- Fotoğraf yükleme butonları -->
                    <div class="profile-image-actions">
                      <button type="button"
                              class="btn btn-primary btn-sm"
                              (click)="triggerFileInput()"
                              [disabled]="isImageUploading"
                              [class.btn-secondary]="remainingUploads <= 0"
                              [class.btn-primary]="remainingUploads > 0">
                        <i class="fas fa-upload"></i>
                        {{ profileImageUrl ? 'Fotoğrafı Değiştir' : 'Fotoğraf Yükle' }}
                      </button>

                      <button *ngIf="profileImageUrl"
                              type="button"
                              class="btn btn-danger btn-sm ms-2"
                              (click)="deleteProfileImage()"
                              [disabled]="isImageUploading">
                        <i class="fas fa-trash"></i>
                        Sil
                      </button>
                    </div>

                    <!-- Kalan upload sayısı bilgisi -->
                  
                  </div>

                  <!-- Dosya seçme input'u (gizli) -->
                  <input type="file"
                         id="profileImageInput"
                         accept="image/jpeg,image/jpg,image/png"
                         (change)="onFileSelected($event)"
                         style="display: none;">

                  <!-- Yükleme durumu -->
                  <div *ngIf="isImageUploading" class="upload-status">
                    <div class="d-flex align-items-center justify-content-center">
                      <app-loading-spinner [size]="'small'" [showText]="false"></app-loading-spinner>
                      <span class="ms-2 text-muted">Fotoğraf yükleniyor...</span>
                    </div>
                  </div>

                  <!-- Yükleme kuralları -->
                  
                </div>
              </div>

              <!-- Kullanıcı Bilgileri -->
              <div class="profile-section">
                <h3 class="section-title">
                  <i class="fas fa-id-card"></i>
                  Kişisel Bilgiler
                </h3>
                <div class="alert alert-info" *ngIf="isAdmin">
                  <i class="fas fa-info-circle"></i>
                  Kişisel bilgilerinizi değiştirmek için lütfen sistem yöneticisiyle iletişime geçiniz.
                </div>
                <div class="alert alert-info" *ngIf="isMember">
                  <i class="fas fa-info-circle"></i>
                  Kişisel bilgilerinizi değiştirmek için lütfen kayıtlı olduğunuz spor salonuna başvurunuz.
                </div>
                <div class="profile-info">
                  <div class="info-row">
                    <div class="info-label">Ad:</div>
                    <div class="info-value">{{ userProfile.user.firstName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">Soyad:</div>
                    <div class="info-value">{{ userProfile.user.lastName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">E-posta:</div>
                    <div class="info-value">{{ userProfile.user.email }}</div>
                  </div>
                </div>
              </div>

              <!-- Lisans Bilgileri (Admin kullanıcılar için) -->
              <div *ngIf="isAdmin && licenseInfo" class="profile-section">
                <h3 class="section-title">
                  <i class="fas fa-certificate"></i>
                  Lisans Bilgileri
                </h3>
                <div class="license-info-container">
                  <div class="license-status-card" [ngClass]="getLicenseStatusClass()">
                    <div class="license-status-header">
                      <i [class]="getLicenseStatusIcon()"></i>
                      <span class="license-status-text">{{ getLicenseStatusMessage() }}</span>
                    </div>
                  </div>
                  <div class="profile-info">
                    <div class="info-row">
                      <div class="info-label">Bitiş Tarihi:</div>
                      <div class="info-value">{{ licenseInfo.endDate | date:'dd/MM/yyyy' }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Üyelik Bilgileri (Eğer varsa) -->
              <div *ngIf="userProfile.member" class="profile-section">
                <h3 class="section-title">
                  <i class="fas fa-dumbbell"></i>
                  Üyelik Bilgileri
                </h3>
                <div class="profile-info">
                  <div class="info-row">
                    <div class="info-label">Telefon:</div>
                    <div class="info-value">{{ userProfile.member.PhoneNumber }}</div>
                  </div>
                  <div *ngIf="userProfile.member.Adress" class="info-row">
                    <div class="info-label">Adres:</div>
                    <div class="info-value">{{ userProfile.member.Adress }}</div>
                  </div>
                  <div *ngIf="userProfile.member.BirthDate" class="info-row">
                    <div class="info-label">Doğum Tarihi:</div>
                    <div class="info-value">{{ userProfile.member.BirthDate }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">Bakiye:</div>
                    <div class="info-value">{{ userProfile.member.Balance }} ₺</div>
                  </div>
                </div>
              </div>

              <!-- Şifre Değiştirme Bölümü -->
              <div class="profile-section">
                <h3 class="section-title">
                  <i class="fas fa-lock"></i>
                  Şifre Yönetimi
                </h3>
                <div class="text-center mb-3">
                  <button class="btn"
                    [ngClass]="isPasswordFormVisible ? 'btn-danger' : 'btn-primary'"
                    (click)="togglePasswordForm()"
                    style="width: 200px; padding: 10px 0;">
                    <i class="fas" [ngClass]="isPasswordFormVisible ? 'fa-times' : 'fa-key'"></i>
                    {{ isPasswordFormVisible ? 'İptal' : 'Şifremi Değiştir' }}
                  </button>
                </div>

                <div *ngIf="isPasswordFormVisible" class="password-form-container">
                  <form [formGroup]="changePasswordForm" (ngSubmit)="changePassword()" class="modern-form">
                    <div class="form-row">
                      <div class="form-group col-md-12">
                        <div class="password-field-container">
                          <div class="password-icon">
                            <i class="fas fa-key"></i>
                          </div>
                          <div class="password-input-wrapper">
                            <label for="currentPassword" class="modern-form-label">Mevcut Şifre</label>
                            <div class="input-group">
                              <input
                                id="currentPassword"
                                [type]="currentPasswordVisible ? 'text' : 'password'"
                                formControlName="currentPassword"
                                class="modern-form-control"
                                placeholder="Mevcut şifreniz"
                                [ngClass]="{'is-invalid': changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched}"
                              >
                              <button type="button" class="password-toggle" (click)="toggleCurrentPasswordVisibility()">
                                <i [class]="currentPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="invalid-feedback" *ngIf="changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched">
                          <span *ngIf="changePasswordForm.get('currentPassword')?.errors?.['required']">Mevcut şifre gerekli</span>
                        </div>
                      </div>
                    </div>

                    <div class="form-row">
                      <div class="form-group col-md-12">
                        <div class="password-field-container">
                          <div class="password-icon">
                            <i class="fas fa-lock"></i>
                          </div>
                          <div class="password-input-wrapper">
                            <label for="newPassword" class="modern-form-label">Yeni Şifre</label>
                            <div class="input-group">
                              <input
                                id="newPassword"
                                [type]="newPasswordVisible ? 'text' : 'password'"
                                formControlName="newPassword"
                                class="modern-form-control"
                                placeholder="Yeni şifreniz (en az 6 karakter)"
                                [ngClass]="{'is-invalid': changePasswordForm.get('newPassword')?.invalid && changePasswordForm.get('newPassword')?.touched}"
                              >
                              <button type="button" class="password-toggle" (click)="toggleNewPasswordVisibility()">
                                <i [class]="newPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="invalid-feedback" *ngIf="changePasswordForm.get('newPassword')?.invalid && changePasswordForm.get('newPassword')?.touched">
                          <span *ngIf="changePasswordForm.get('newPassword')?.errors?.['required']">Yeni şifre gerekli</span>
                          <span *ngIf="changePasswordForm.get('newPassword')?.errors?.['minlength']">Şifre en az 6 karakter olmalıdır</span>
                        </div>
                      </div>
                    </div>

                    <div class="form-row">
                      <div class="form-group col-md-12">
                        <div class="password-field-container">
                          <div class="password-icon">
                            <i class="fas fa-lock"></i>
                          </div>
                          <div class="password-input-wrapper">
                            <label for="confirmPassword" class="modern-form-label">Şifre Tekrarı</label>
                            <div class="input-group">
                              <input
                                id="confirmPassword"
                                [type]="confirmPasswordVisible ? 'text' : 'password'"
                                formControlName="confirmPassword"
                                class="modern-form-control"
                                placeholder="Yeni şifrenizi tekrar girin"
                                [ngClass]="{'is-invalid': (changePasswordForm.get('confirmPassword')?.invalid && changePasswordForm.get('confirmPassword')?.touched) || changePasswordForm.hasError('mismatch')}"
                              >
                              <button type="button" class="password-toggle" (click)="toggleConfirmPasswordVisibility()">
                                <i [class]="confirmPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="invalid-feedback" *ngIf="(changePasswordForm.get('confirmPassword')?.invalid && changePasswordForm.get('confirmPassword')?.touched) || changePasswordForm.hasError('mismatch')">
                          <span *ngIf="changePasswordForm.get('confirmPassword')?.errors?.['required']">Şifre tekrarı gerekli</span>
                          <span *ngIf="changePasswordForm.hasError('mismatch')">Şifreler eşleşmiyor</span>
                        </div>
                      </div>
                    </div>

                    <div class="password-requirements card mt-3 mb-4">
                      <div class="card-body">
                        <h5 class="card-title">Şifre Gereksinimleri:</h5>
                        <ul class="requirements-list">
                          <li [ngClass]="{'fulfilled': changePasswordForm.get('newPassword')?.value?.length >= 6}">
                            <i [class]="changePasswordForm.get('newPassword')?.value?.length >= 6 ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
                            En az 6 karakter
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div class="form-group text-center mt-4">
                      <button
                        type="submit"
                        [disabled]="changePasswordForm.invalid || isPasswordChanging || changePasswordForm.hasError('mismatch')"
                        class="btn btn-success"
                        style="width: 200px; padding: 10px 0;"
                      >
                        <span *ngIf="!isPasswordChanging">Şifreyi Değiştir</span>
                        <app-loading-spinner *ngIf="isPasswordChanging" [size]="'small'" [showText]="false"></app-loading-spinner>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- Profil bulunamadı mesajı -->
            <div *ngIf="!isLoading && !userProfile" class="alert alert-warning">
              <i class="fas fa-exclamation-triangle"></i>
              Profil bilgileriniz yüklenemedi. Lütfen daha sonra tekrar deneyin.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>